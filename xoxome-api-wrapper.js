/**
 * XoxoMe 邮箱服务 API 封装类
 * 整合登录认证和邮箱服务功能
 */
class XoxoMeAPI {
    constructor(config = {}) {
        this.config = {
            apiBase: 'https://mail.xoxome.online',
            loginUrl: 'https://mail.xoxome.online/api/auth/login',
            username: config.username || 'halo',
            password: config.password || '12345678aka',
            ...config
        };

        this.token = null;
        this.user = null;
        this.availableSuffixes = null; // 缓存可用的域名后缀
    }

    /**
     * 登录获取 token
     * @returns {Promise<Object>} 登录响应数据
     */
    async login() {
        try {
            const response = await fetch(this.config.loginUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: this.config.username,
                    password: this.config.password
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            
            if (result.success) {
                this.token = result.token;
                this.user = result.user;
                console.log('登录成功:', result);
                return result;
            } else {
                throw new Error(result.message || '登录失败');
            }
        } catch (error) {
            console.error('登录失败:', error);
            throw error;
        }
    }

    /**
     * 确保已登录，如果未登录则自动登录
     */
    async ensureLoggedIn() {
        if (!this.token) {
            await this.login();
        }
    }

    /**
     * 获取请求头
     * @returns {Object} 包含认证信息的请求头
     */
    getHeaders() {
        const headers = {
            'Accept': 'application/json',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        };

        if (this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
            headers['Cookie'] = `token=${this.token}`;
        }
        return headers;
    }

    /**
     * 获取可用的邮箱域名后缀
     * @returns {Promise<Array>} 域名后缀列表
     */
    async getSuffixes() {
        await this.ensureLoggedIn();

        try {
            const response = await fetch(`${this.config.apiBase}/api/email/suffixes`, {
                headers: this.getHeaders()
            });
            const data = await response.json();
            const suffixes = data.success ? data.data : [];
            this.availableSuffixes = suffixes; // 缓存后缀列表
            return suffixes;
        } catch (error) {
            console.error('获取域名后缀失败:', error);
            throw error;
        }
    }

    /**
     * 随机选择一个可用的域名后缀
     * @returns {Promise<string>} 随机选择的域名后缀
     */
    async getRandomSuffix() {
        // 如果没有缓存的后缀列表，先获取
        if (!this.availableSuffixes || this.availableSuffixes.length === 0) {
            await this.getSuffixes();
        }

        if (!this.availableSuffixes || this.availableSuffixes.length === 0) {
            throw new Error('没有可用的域名后缀');
        }

        // 随机选择一个后缀
        const randomIndex = Math.floor(Math.random() * this.availableSuffixes.length);
        return this.availableSuffixes[randomIndex];
    }

    /**
     * 创建临时邮箱
     * @param {string} suffix - 邮箱后缀，如果不提供则随机选择一个
     * @returns {Promise<Object>} 包含邮箱地址和sessionId的对象
     */
    async createEmail(suffix = null) {
        await this.ensureLoggedIn();
        // 如果没有指定后缀，则随机选择一个
        const emailSuffix = suffix || await this.getRandomSuffix();
        console.log('使用域名后缀:', emailSuffix);

        try {
            const response = await fetch(`${this.config.apiBase}/api/email/generate`, {
                method: 'POST',
                headers: {
                    ...this.getHeaders(),
                    'Content-Type': 'application/json',
                    'Referer': 'https://mail.xoxome.online/dashboard',
                    'Origin': 'https://mail.xoxome.online'
                },
                body: JSON.stringify({
                    suffix: emailSuffix
                })
            });

            const data = await response.json();

            if (data.success) {
                // 生成 sessionId 用于后续获取邮件
                const sessionId = `sync_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

                return {
                    email: data.data.email,
                    prefix: data.data.prefix,
                    suffix: data.data.suffix,
                    sessionId: sessionId
                };
            } else {
                throw new Error(data.message || '创建邮箱失败');
            }
        } catch (error) {
            console.error('创建邮箱失败:', error);
            throw error;
        }
    }

    /**
     * 获取邮件列表
     * @param {string} sessionId - 会话ID
     * @returns {Promise<Array>} 邮件列表
     */
    async getEmails(sessionId) {
        await this.ensureLoggedIn();
        
        try {
            const response = await fetch(
                `${this.config.apiBase}/api/emails/imap-fetch?sessionId=${sessionId}`,
                {
                    headers: {
                        ...this.getHeaders(),
                        'Referer': 'https://mail.xoxome.online/dashboard'
                    }
                }
            );

            const data = await response.json();
            return data.success ? data.data : [];
        } catch (error) {
            console.error('获取邮件失败:', error);
            throw error;
        }
    }

    /**
     * 从邮件内容中提取验证码
     * @param {string} emailContent - 邮件内容（文本或HTML）
     * @returns {string|null} 验证码或null
     */
    extractVerificationCode(emailContent) {
        const patterns = [
            /验证码[：:\s]*(\d{4,8})/i,
            /verification code[：:\s]*(\d{4,8})/i,
            /code[：:\s]*(\d{4,8})/i,
            /(\d{6})/,  // 6位数字
            /(\d{4})/   // 4位数字
        ];
        
        for (const pattern of patterns) {
            const match = emailContent.match(pattern);
            if (match && match[1]) {
                return match[1];
            }
        }
        return null;
    }

    /**
     * 等待并获取验证码
     * @param {string} sessionId - 会话ID
     * @param {number} maxRetries - 最大重试次数
     * @param {number} retryInterval - 重试间隔（毫秒）
     * @returns {Promise<string>} 验证码
     */
    async waitForVerificationCode(sessionId, maxRetries = 6, retryInterval = 5000) {
        for (let attempt = 0; attempt < maxRetries; attempt++) {
            console.log(`尝试获取验证码 (第 ${attempt + 1}/${maxRetries} 次)...`);
            
            try {
                const emails = await this.getEmails(sessionId);
                
                if (emails && emails.length > 0) {
                    // 检查最新的邮件
                    const latestEmail = emails[0];
                    const emailContent = latestEmail.text || latestEmail.html || '';
                    const code = this.extractVerificationCode(emailContent);
                    
                    if (code) {
                        console.log('成功获取验证码:', code);
                        return code;
                    }
                }
                
                if (attempt < maxRetries - 1) {
                    console.log(`未获取到验证码，${retryInterval/1000}秒后重试...`);
                    await new Promise(resolve => setTimeout(resolve, retryInterval));
                }
            } catch (error) {
                console.error('获取验证码出错:', error);
                if (attempt < maxRetries - 1) {
                    await new Promise(resolve => setTimeout(resolve, retryInterval));
                }
            }
        }
        
        throw new Error(`经过 ${maxRetries} 次尝试后仍未获取到验证码`);
    }

    /**
     * 完整的邮箱验证流程
     * @param {string} suffix - 邮箱后缀（可选）
     * @returns {Promise<Object>} 包含邮箱地址和验证码的对象
     */
    async getEmailAndCode(suffix = null) {
        try {
            // 1. 创建邮箱
            const emailInfo = await this.createEmail(suffix);
            console.log('邮箱创建成功:', emailInfo.email);
            
            // 2. 等待验证码
            const code = await this.waitForVerificationCode(emailInfo.sessionId);
            
            return {
                email: emailInfo.email,
                code: code,
                sessionId: emailInfo.sessionId
            };
        } catch (error) {
            console.error('邮箱验证流程失败:', error);
            throw error;
        }
    }
}

// 导出类供其他文件使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = XoxoMeAPI;
}

// 使用示例
async function example() {
    const api = new XoxoMeAPI({
        username: 'halo',
        password: '12345678aka'
    });

    try {
        // 方式一：分步操作
        await api.login();
        const suffixes = await api.getSuffixes();
        console.log('可用后缀:', suffixes);

        // 创建邮箱时会自动随机选择域名后缀
        const emailInfo = await api.createEmail();
        console.log('创建的邮箱:', emailInfo.email);

        const code = await api.waitForVerificationCode(emailInfo.sessionId);
        console.log('获取的验证码:', code);

        // 方式二：一键获取邮箱和验证码（会自动随机选择域名）
        // const result = await api.getEmailAndCode();
        // console.log('邮箱:', result.email, '验证码:', result.code);

        // 方式三：指定特定的域名后缀
        // const emailInfo2 = await api.createEmail('7758.baby');
        // console.log('指定域名的邮箱:', emailInfo2.email);

    } catch (error) {
        console.error('操作失败:', error);
    }
}

// 如果在浏览器环境中直接运行
if (typeof window !== 'undefined') {
    window.XoxoMeAPI = XoxoMeAPI;
    // example(); // 取消注释以运行示例
}
